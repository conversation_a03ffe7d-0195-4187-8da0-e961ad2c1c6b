2025-08-14 15:14:40.617 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-14 15:14:40.617 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-14 15:14:40.617 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-14 15:14:40.618 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-14 15:14:40.619 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-14 15:14:40.620 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-14 15:14:44.376 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-14 15:14:44.378 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-14 15:14:44.378 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-14 15:14:44.379 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-14 15:14:44.379 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-14 15:14:44.380 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-14 15:14:44.381 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-14 15:14:44.381 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-14 15:14:44.382 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-14 15:14:44.383 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-14 15:14:44.384 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-14 15:14:44.424 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-14 15:14:44.426 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-14 15:14:44.432 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-14 15:14:44.439 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-14 15:14:44.465 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-14 15:14:44.470 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-14 15:14:44.472 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-14 15:14:44.473 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-14 15:14:44.477 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-14 15:14:44.477 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-14 15:14:44.477 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-14 15:14:44.477 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-14 15:14:44.477 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11506 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-14 15:14:44.477 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-14 15:14:44.483 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11361 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-14 15:14:44.484 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11399 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-14 15:14:44.564 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-14 15:14:44.564 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-14 15:14:44.564 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-14 15:14:44.564 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-14 15:14:44.585 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-14 15:14:44.585 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-14 15:14:44.585 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-14 15:14:44.585 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-14 15:14:44.585 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-14 15:14:44.585 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-14 15:14:44.585 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-14 15:14:44.585 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-14 15:14:44.585 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 21.5ms
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.prototype_main_window:__init__:3613 | 🚀 性能管理器已集成
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.prototype_main_window:__init__:3615 | ✅ 新架构集成成功！
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3728 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-14 15:14:44.627 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3693 | ✅ 新架构事件监听器设置完成
2025-08-14 15:14:44.627 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-14 15:14:44.828 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-14 15:14:44.903 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-14 15:14:45.204 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2689 | 菜单栏创建完成
2025-08-14 15:14:45.204 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-14 15:14:45.220 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-14 15:14:45.220 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-14 15:14:45.220 | INFO     | src.gui.prototype.prototype_main_window:__init__:2664 | 菜单栏管理器初始化完成
2025-08-14 15:14:45.220 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-14 15:14:45.220 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5583 | 管理器设置完成，包含增强版表头管理器
2025-08-14 15:14:45.220 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5588 | 🔧 开始应用窗口级Material Design样式...
2025-08-14 15:14:45.220 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-14 15:14:45.220 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-14 15:14:45.220 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5595 | ✅ 窗口级样式应用成功
2025-08-14 15:14:45.220 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5636 | ✅ 响应式样式监听设置完成
2025-08-14 15:14:45.235 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-14 15:14:45.266 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-14 15:14:45.266 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-14 15:14:45.266 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-14 15:14:45.282 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-14 15:14:45.282 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:45.297 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1372 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-14 15:14:45.313 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1725 | 使用兜底数据加载导航
2025-08-14 15:14:45.313 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:14:45.313 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:14:45.313 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:14:45.328 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-14 15:14:45.360 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-14 15:14:45.383 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-14 15:14:45.387 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-14 15:14:45.391 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-14 15:14:45.417 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:45.418 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1372 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-14 15:14:45.431 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:14:45.452 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:14:45.453 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:14:45.489 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-14 15:14:45.493 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1295 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-14 15:14:45.494 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:14:45.496 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:45.498 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:14:45.499 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1492 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-14 15:14:45.500 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:14:45.842 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-14 15:14:45.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2138 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-14 15:14:45.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1365 | 快捷键注册完成: 18/18 个
2025-08-14 15:14:45.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1805 | 拖拽排序管理器初始化完成
2025-08-14 15:14:45.903 | INFO     | src.modules.data_management.data_flow_validator:__init__:78 | 🔧 [数据验证器] 初始化完成，验证级别: moderate
2025-08-14 15:14:45.907 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-14 15:14:45.927 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-14 15:14:45.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2173 | 🔧 [排序修复] 数据流验证器和状态管理器初始化成功
2025-08-14 15:14:45.929 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-14 15:14:45.930 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-14 15:14:45.931 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-14 15:14:45.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2225 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-14 15:14:45.950 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-14 15:14:45.951 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-14 15:14:45.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2272 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-14 15:14:45.954 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1552 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-14 15:14:45.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-14 15:14:45.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-14 15:14:45.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-14 15:14:45.962 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-14 15:14:45.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2279 | 列宽管理器初始化完成
2025-08-14 15:14:45.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2406 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-14 15:14:45.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2293 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-14 15:14:45.976 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:14:45.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:14:45.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:14:45.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:14:45.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:14:45.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:14:46.006 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:14:46.006 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:14:46.023 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:14:46.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:14:46.025 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:14:46.036 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-14 15:14:46.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 55.3ms
2025-08-14 15:14:46.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:14:46.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:14:46.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-14 15:14:46.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:14:46.079 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:14:46.092 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-14 15:14:46.104 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-14 15:14:46.108 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-14 15:14:46.148 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-14 15:14:46.197 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-14 15:14:46.197 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-14 15:14:46.201 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5545 | 快捷键设置完成
2025-08-14 15:14:46.201 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5502 | 主窗口UI设置完成。
2025-08-14 15:14:46.203 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5739 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-14 15:14:46.203 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5771 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-14 15:14:46.204 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5783 | ✅ 已连接分页刷新信号到主窗口
2025-08-14 15:14:46.205 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5784 | ✅ 已连接分页组件事件到新架构
2025-08-14 15:14:46.207 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5795 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-14 15:14:46.207 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5798 | 信号连接设置完成
2025-08-14 15:14:46.209 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6783 | 已加载字段映射信息，共0个表的映射
2025-08-14 15:14:46.225 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:14:46.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:14:46.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:14:46.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:14:46.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:14:46.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:14:46.234 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:14:46.235 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:14:46.245 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:14:46.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:14:46.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 19.6ms
2025-08-14 15:14:46.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:14:46.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:14:46.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-14 15:14:46.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:14:46.259 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:14:46.260 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:14:46.262 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8240 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-14 15:14:46.263 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:14:46.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:14:46.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:14:46.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:14:46.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:14:46.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:14:46.297 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:14:46.298 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:14:46.300 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:14:46.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:14:46.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 61.5ms
2025-08-14 15:14:46.351 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:14:46.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:14:46.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-14 15:14:46.356 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:14:46.357 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:14:46.359 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 22
2025-08-14 15:14:46.380 | INFO     | src.gui.prototype.prototype_main_window:__init__:3667 | 原型主窗口初始化完成
2025-08-14 15:14:46.420 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-14 15:14:46.445 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1365 | 执行延迟的自动选择最新数据...
2025-08-14 15:14:46.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:14:46.447 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:14:46.449 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:46.450 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:14:46.450 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:14:46.451 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1388 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-14 15:14:46.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:14:46.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:14:46.596 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:14:46.600 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:46.602 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:14:46.602 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:14:46.606 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-14 15:14:46.606 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-14 15:14:47.111 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9142 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-14 15:14:47.111 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9052 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-14 15:14:47.115 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9066 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-14 15:14:47.116 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9600 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-14 15:14:47.141 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9072 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-14 15:14:47.451 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:14:47.452 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:14:47.455 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:14:47.455 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1503 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-14 15:14:47.456 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:19:40.061 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-14 15:19:40.063 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 05月 > 离休人员
2025-08-14 15:19:40.066 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-14 15:19:40.067 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:19:40.068 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '离休人员'] -> salary_data_2025_05_retired_employees
2025-08-14 15:19:40.069 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:19:40.070 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_retired_employees 的缓存
2025-08-14 15:19:40.072 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 2 个表格到表头管理器
2025-08-14 15:19:40.072 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-14 15:19:40.074 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.86ms
2025-08-14 15:19:40.079 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:19:40.079 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_05_retired_employees（通过事件系统）
2025-08-14 15:19:40.080 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:19:40.081 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:19:40.082 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1040 | 🔧 [P1修复] 找到 3 个总表 (尝试 1/5)
2025-08-14 15:19:40.083 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7403 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_retired_employees 尚未创建，显示空表格等待数据导入
2025-08-14 15:19:40.083 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8163 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-14 15:19:40.085 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-14 15:19:40.089 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-14 15:19:40.095 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:19:40.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:19:40.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:19:40.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:19:40.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:19:40.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:19:40.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:19:40.104 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:19:40.106 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:19:40.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:19:40.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:19:40.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 14.0ms
2025-08-14 15:19:40.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:19:40.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:19:40.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:19:40.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:19:40.119 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:19:40.120 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:19:40.125 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 15
2025-08-14 15:19:40.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:19:50.034 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-14 15:19:50.038 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1295 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-14 15:19:50.038 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:19:50.041 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:19:50.041 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:19:50.041 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1492 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-14 15:19:50.042 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:19:51.042 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:19:51.043 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:19:51.046 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:19:51.047 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:19:52.048 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1295 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-14 15:19:52.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:19:52.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:19:52.053 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:19:52.054 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:19:53.054 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:19:53.055 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:19:53.058 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1534 | 未找到任何工资数据表
2025-08-14 15:19:53.059 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1503 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-14 15:19:53.060 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1300 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-14 15:19:53.727 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-14 15:19:53.727 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 异动人员表
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:19:53.744 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7978 | 🔧 [表名生成] 路径第一级不是'工资表': 异动人员表
2025-08-14 15:19:53.744 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:19:53.744 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:19:53.758 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:19:53.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:19:53.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:19:53.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 17.2ms
2025-08-14 15:19:53.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:19:53.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:19:53.781 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:19:53.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:19:53.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:19:53.783 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:19:53.784 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8163 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-14 15:19:53.790 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-14 15:19:53.791 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:19:53.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:19:53.797 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:19:53.801 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:19:53.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:19:53.806 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:19:53.808 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:19:53.808 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:19:53.811 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:19:53.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:19:53.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 14.7ms
2025-08-14 15:19:53.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:19:53.825 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:19:53.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:19:53.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:19:53.858 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:19:53.863 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 15
2025-08-14 15:19:53.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:19:53.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:19:59.033 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-14 15:20:36.379 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 离休人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-14 15:20:36.380 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表
2025-08-14 15:20:36.385 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-14 15:20:36.386 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:20:36.387 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7983 | 🔧 [表名生成] 导航路径不完整(1层): ['工资表']
2025-08-14 15:20:36.388 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:20:36.390 | INFO     | src.gui.prototype.prototype_main_window:set_data:798 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-14 15:20:36.390 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:20:36.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:20:36.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:20:36.393 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:20:36.398 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:20:36.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:20:36.402 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:20:36.402 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:20:36.405 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:20:36.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:20:36.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:20:36.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 14.2ms
2025-08-14 15:20:36.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:20:36.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:20:36.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:20:36.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:20:36.418 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:20:36.418 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:20:36.419 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8163 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-14 15:20:36.420 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-14 15:20:36.430 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:20:36.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:20:36.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:20:36.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:20:36.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:20:36.435 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:20:36.436 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:20:36.437 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:20:36.444 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:20:36.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:20:36.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 13.7ms
2025-08-14 15:20:36.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:20:36.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:20:36.456 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:20:36.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:20:36.459 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:20:36.462 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 15
2025-08-14 15:20:36.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:20:36.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:20:49.137 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-14 15:20:49.138 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6031 | 接收到数据导入请求，推断的目标路径: 工资表。打开导入对话框。
2025-08-14 15:20:49.142 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-14 15:20:49.143 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-14 15:20:49.145 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-14 15:20:49.163 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:20:49.202 | INFO     | src.gui.main_dialogs:_get_template_fields:1874 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-14 15:20:49.202 | INFO     | src.gui.main_dialogs:_init_field_mapping:1861 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-14 15:20:49.242 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-14 15:20:49.242 | INFO     | src.gui.main_dialogs:_apply_default_settings:2212 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-14 15:20:49.242 | INFO     | src.gui.main_dialogs:_setup_tooltips:2467 | 工具提示设置完成
2025-08-14 15:20:49.242 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2506 | 快捷键设置完成
2025-08-14 15:20:49.242 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-14 15:20:49.242 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:84 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-14 15:20:49.258 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6042 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-14 15:20:57.951 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:20:59.735 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-14 15:20:59.736 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2247 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-14 15:21:19.183 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:21:19.402 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-14 15:21:19.404 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:214 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-14 15:21:19.602 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:225 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:21:19.605 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-14 15:21:19.606 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:21:19.713 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-14 15:21:19.716 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:21:19.718 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-14 15:21:19.719 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-14 15:21:19.722 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:21:19.722 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-14 15:21:19.729 | INFO     | src.modules.data_import.excel_importer:import_data:301 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-14 15:21:19.730 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 离休人员工资表 使用智能默认配置
2025-08-14 15:21:19.731 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-14 15:21:19.732 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-14 15:21:19.733 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-14 15:21:19.739 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_retired_employees
2025-08-14 15:21:19.740 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_retired_employees
2025-08-14 15:21:19.740 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_08_retired_employees 生成标准化字段映射: 21 个字段
2025-08-14 15:21:19.740 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-14 15:21:19.740 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-08-14 15:21:19.756 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_08_retired_employees
2025-08-14 15:21:19.756 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_08_retired_employees (模板: retired_employees)
2025-08-14 15:21:19.872 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-14 15:21:19.873 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:21:19.875 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-14 15:21:19.894 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。
2025-08-14 15:21:19.900 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:21:20.018 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:21:20.020 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-14 15:21:20.023 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:21:20.024 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-14 15:21:20.026 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 退休人员工资表 使用智能默认配置
2025-08-14 15:21:20.031 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-14 15:21:20.032 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-14 15:21:20.034 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-14 15:21:20.041 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_pension_employees
2025-08-14 15:21:20.042 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_pension_employees
2025-08-14 15:21:20.044 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_08_pension_employees 生成标准化字段映射: 32 个字段
2025-08-14 15:21:20.049 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-14 15:21:20.050 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-08-14 15:21:20.053 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_08_pension_employees
2025-08-14 15:21:20.061 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_08_pension_employees (模板: pension_employees)
2025-08-14 15:21:20.076 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-08-14 15:21:20.077 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:21:20.079 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-14 15:21:20.096 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。
2025-08-14 15:21:20.101 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:21:20.234 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:21:20.237 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-14 15:21:20.241 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_active_employees
2025-08-14 15:21:20.244 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_active_employees
2025-08-14 15:21:20.260 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_08_active_employees 生成标准化字段映射: 28 个字段
2025-08-14 15:21:20.276 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-14 15:21:20.276 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-08-14 15:21:20.276 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_08_active_employees
2025-08-14 15:21:20.291 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_08_active_employees (模板: active_employees)
2025-08-14 15:21:20.307 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-14 15:21:20.307 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:21:20.307 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-14 15:21:20.354 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。
2025-08-14 15:21:20.354 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:21:20.497 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:21:20.500 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-14 15:21:20.502 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:21:20.504 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-14 15:21:20.506 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 A岗职工 使用智能默认配置
2025-08-14 15:21:20.511 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-14 15:21:20.512 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-14 15:21:20.512 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-14 15:21:20.517 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_a_grade_employees
2025-08-14 15:21:20.518 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_a_grade_employees
2025-08-14 15:21:20.519 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_08_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-14 15:21:20.527 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet A岗职工 数据处理完成: 62 行
2025-08-14 15:21:20.528 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-08-14 15:21:20.537 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_08_a_grade_employees
2025-08-14 15:21:20.543 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_08_a_grade_employees (模板: a_grade_employees)
2025-08-14 15:21:20.553 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-08-14 15:21:20.554 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:21:20.560 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-14 15:21:20.576 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。
2025-08-14 15:21:20.579 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:244 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-14 15:21:20.581 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1481 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-14 15:21:20.584 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6061 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-14 15:21:20.587 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6072 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 08月 > 全部在职人员'
2025-08-14 15:21:20.597 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6090 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:20.604 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1740 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-14 15:21:20.617 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-14 15:21:20.619 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表
2025-08-14 15:21:20.620 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-14 15:21:20.621 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:20.623 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7983 | 🔧 [表名生成] 导航路径不完整(1层): ['工资表']
2025-08-14 15:21:20.642 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:20.676 | INFO     | src.gui.prototype.prototype_main_window:set_data:798 | 空数据输入发生 2 次（2s 窗口），将显示空表提示
2025-08-14 15:21:20.677 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:21:20.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:21:20.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:21:20.681 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:21:20.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:21:20.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:21:20.688 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:21:20.689 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:21:20.693 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:21:20.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:21:20.713 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:21:20.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 35.0ms
2025-08-14 15:21:20.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:20.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:20.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:21:20.736 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:20.736 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:21:20.738 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:21:20.739 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8163 | 🔧 [数据流追踪] 使用离休人员表头: 15个字段
2025-08-14 15:21:20.740 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_retired_employees 的专用表头: 15个字段
2025-08-14 15:21:20.742 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:21:20.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:21:20.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:21:20.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-14 15:21:20.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:21:20.762 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:21:20.765 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:21:20.766 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:21:20.768 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:21:20.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:21:20.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 32.1ms
2025-08-14 15:21:20.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:20.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:20.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:21:20.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:20.792 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:21:20.793 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 15
2025-08-14 15:21:20.800 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:20.810 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:20.817 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1712 | 动态加载了 1 个月份的工资数据导航
2025-08-14 15:21:20.824 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:21:20.824 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:21:20.830 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年', '工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-14 15:21:20.834 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:20.836 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_retired_employees -> None
2025-08-14 15:21:20.837 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:20.839 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:21:20.841 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:21:20.841 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-14 15:21:20.849 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:21:20.856 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-14 15:21:20.860 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 3.12ms
2025-08-14 15:21:20.860 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:21:20.861 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-14 15:21:20.863 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:21:20.864 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:21:20.867 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:21:20.878 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-14 15:21:20.880 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=15.8ms
2025-08-14 15:21:21.015 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-14 15:21:21.016 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-14 15:21:21.019 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-14 15:21:21.020 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-14 15:21:21.021 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:21:21.021 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-14 15:21:21.022 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-14 15:21:21.027 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-14 15:21:21.033 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-14 15:21:21.036 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-14 15:21:21.037 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_07_active_employees 自动生成字段类型配置
2025-08-14 15:21:21.039 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_07_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-14 15:21:21.040 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_07_active_employees
2025-08-14 15:21:21.041 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_retired_employees 自动生成字段类型配置
2025-08-14 15:21:21.046 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-14 15:21:21.047 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_retired_employees
2025-08-14 15:21:21.048 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_pension_employees 自动生成字段类型配置
2025-08-14 15:21:21.049 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-14 15:21:21.049 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_pension_employees
2025-08-14 15:21:21.050 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_active_employees 自动生成字段类型配置
2025-08-14 15:21:21.051 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-14 15:21:21.052 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_active_employees
2025-08-14 15:21:21.053 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_a_grade_employees 自动生成字段类型配置
2025-08-14 15:21:21.053 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-14 15:21:21.054 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_a_grade_employees
2025-08-14 15:21:21.066 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 11个表的existing_display_fields为空: salary_data_2025_07_active_employees, active_employees, salary_data_2025_08_retired_employees 等11个表']
2025-08-14 15:21:21.066 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:21:21.067 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_08_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-14 15:21:21.069 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_08_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-14 15:21:21.070 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_08_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:21:21.071 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_08_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:21:21.072 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-14 15:21:21.073 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-14 15:21:21.081 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 part_time_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'hourly_rate', 'hours_worked']...
2025-08-14 15:21:21.082 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 contract_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'base_salary', 'performance_bonus']...
2025-08-14 15:21:21.083 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:21:21.084 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 11 项修复操作
2025-08-14 15:21:21.093 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-14 15:21:21.107 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 5
2025-08-14 15:21:21.108 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:21:21.108 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-14 15:21:21.109 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-14 15:21:21.110 | INFO     | src.modules.format_management.unified_format_manager:__init__:1079 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-08-14 15:21:21.111 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-14 15:21:21.112 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:21.112 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:21:21.124 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:21:21.126 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:21:21.128 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:21:21.132 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:21:21.134 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:21:21.135 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 25/25 个字段
2025-08-14 15:21:21.136 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 3个
2025-08-14 15:21:21.142 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-14 15:21:21.143 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:21:21.150 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:21:21.170 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-14 15:21:21.172 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-14 15:21:21.173 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755156081173-44a99969
2025-08-14 15:21:21.175 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755156081173-44a99969
2025-08-14 15:21:21.175 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:21:21.176 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-14 15:21:21.183 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:21:21.205 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:21:21.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:21:21.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:21.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:21.220 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 1000 -> 50
2025-08-14 15:21:21.220 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:21:21.231 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:21.232 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:21:21.248 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:21:21.250 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:21:21.252 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:21:21.253 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:21:21.254 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:21:21.255 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:21:21.263 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:21:21.263 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:21.264 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:21.290 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-14 15:21:21.293 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-14 15:21:21.413 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:21:21.414 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:21:21.434 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时19.6ms, 平均每行0.39ms
2025-08-14 15:21:21.448 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-08-14 15:21:21.449 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=19.6ms, 策略=small_dataset
2025-08-14 15:21:21.453 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:21:21.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:21:21.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:21:21.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:21:21.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:21:21.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:21:21.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:21:21.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:21.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:21.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:21:21.473 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:21:21.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:21:21.479 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:21:21.483 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-14 15:21:21.486 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-14 15:21:21.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 266.7ms
2025-08-14 15:21:21.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:21.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:21.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:21.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:21.504 | INFO     | src.core.pagination_state_manager:__init__:63 | 🔧 [立即修复] 分页状态管理器初始化完成
2025-08-14 15:21:21.505 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:21:21.506 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:21:21.507 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:21.508 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-14 15:21:21.509 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:21:21.510 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755156081173-44a99969 | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:21:21.511 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156081173-44a99969
2025-08-14 15:21:21.512 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156081173-44a99969
2025-08-14 15:21:21.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:21:21.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:21:21.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:21:21.525 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:21:21.525 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:21:21.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:21.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:21:21.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:21:21.528 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:21.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:21:21.529 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:21:21.597 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:21:21.597 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:21:21.601 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:21:21.602 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:21:21.603 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 15:21:21.604 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:21:21.605 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:21.605 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:21:21.606 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:21:21.609 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年', '工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-14 15:21:21.614 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表
2025-08-14 15:21:21.615 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:21:21.616 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:21.617 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7983 | 🔧 [表名生成] 导航路径不完整(1层): ['工资表']
2025-08-14 15:21:21.622 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:21.623 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:21:21.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:21:21.630 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:21:21.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:21:21.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:21:21.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:21:21.638 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:21:21.643 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:21:21.644 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:21:21.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:21:21.645 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-14 15:21:21.649 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-14 15:21:21.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 14.6ms
2025-08-14 15:21:21.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:21.657 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:21.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:21:21.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.663 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:21.665 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:21:21.674 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8153 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-14 15:21:21.675 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-14 15:21:21.677 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:21:21.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:21:21.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:21:21.680 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:21:21.681 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:21:21.690 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:21:21.691 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:21:21.692 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:21:21.694 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:21:21.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:21:21.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 21.2ms
2025-08-14 15:21:21.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:21.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:21.709 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:21:21.710 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:21.714 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:21:21.721 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 22
2025-08-14 15:21:21.722 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-14 15:21:21.722 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1788 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-14 15:21:21.727 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-14 15:21:21.735 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1740 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-14 15:21:21.737 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-14 15:21:21.738 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 异动人员表
2025-08-14 15:21:21.739 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:21:21.740 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:21.742 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7978 | 🔧 [表名生成] 路径第一级不是'工资表': 异动人员表
2025-08-14 15:21:21.743 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:21:21.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:21:21.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:21:21.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:21:21.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:21:21.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:21:21.752 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:21:21.752 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:21:21.755 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:21:21.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:21:21.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:21:21.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 24.7ms
2025-08-14 15:21:21.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:21.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:21.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:21:21.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:21:21.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:21.792 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:21:21.799 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:21:21.804 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:21.811 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:21:21.811 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:21:21.813 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:21:21.815 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:21:21.821 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-14 15:21:21.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-14 15:21:21.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1788 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-14 15:21:21.830 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-14 15:21:21.837 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1295 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-14 15:21:21.841 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:21:21.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:21.846 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1577 | 找到最新工资数据路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:21.850 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1453 | 🔧 [P1-2修复] 成功获取到最新路径
2025-08-14 15:21:21.860 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年', '工资表 > 2025年 > 08月']
2025-08-14 15:21:21.861 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:21.862 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:21:21.863 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:21.866 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:21:21.867 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:21:21.868 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-14 15:21:21.880 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:21:21.880 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-14 15:21:21.885 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 4.80ms
2025-08-14 15:21:21.889 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:21:21.890 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-14 15:21:21.891 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:21:21.892 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 15:21:21.895 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755156081895-155b4021-C
2025-08-14 15:21:21.896 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755156081895-155b4021-C
2025-08-14 15:21:21.897 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:21:21.922 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-14 15:21:21.929 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:21:21.934 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:21:21.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:21:21.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:21.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:21.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:21:21.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:21:21.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:21.996 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:21:22.017 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:21:22.018 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:21:22.022 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:21:22.024 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:21:22.025 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:21:22.026 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:21:22.028 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:21:22.031 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:22.032 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:22.079 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:21:22.080 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:21:22.102 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时21.8ms, 平均每行0.44ms
2025-08-14 15:21:22.106 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=21.8ms, 策略=small_dataset
2025-08-14 15:21:22.108 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:21:22.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:21:22.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:21:22.110 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:21:22.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:21:22.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:21:22.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:21:22.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:22.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:22.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:21:22.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:21:22.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:21:22.165 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:21:22.168 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-14 15:21:22.170 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-14 15:21:22.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 209.9ms
2025-08-14 15:21:22.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:22.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:22.178 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:22.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:22.182 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:21:22.202 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:21:22.210 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:22.221 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-14 15:21:22.234 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:21:22.235 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755156081895-155b4021-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:21:22.236 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156081895-155b4021-C
2025-08-14 15:21:22.237 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156081895-155b4021-C
2025-08-14 15:21:22.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:21:22.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:21:22.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:21:22.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:21:22.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:21:22.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:22.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:21:22.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:21:22.261 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:22.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:21:22.269 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:21:22.329 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:21:22.331 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:21:22.340 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:21:22.341 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:21:22.342 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 15:21:22.344 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:21:22.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:22.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-14 15:21:22.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:22.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-14 15:21:22.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-14 15:21:22.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-14 15:21:22.538 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:22.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:23.164 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6223 | 检查是否需要更新导航面板: ['工资表', '2025年', '08月', '全部在职人员']
2025-08-14 15:21:23.164 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6227 | 检测到工资数据导入，开始刷新导航面板
2025-08-14 15:21:23.168 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6231 | 使用强制刷新方法
2025-08-14 15:21:23.169 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1926 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-14 15:21:23.170 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1934 | 找到工资表节点: 工资表
2025-08-14 15:21:23.172 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年 > 05月 > 离休人员', '工资表 > 2025年', '工资表 > 2025年 > 08月']
2025-08-14 15:21:23.173 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.173 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:21:23.174 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:21:23.175 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:21:23.180 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:21:23.182 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-14 15:21:23.186 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:21:23.187 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-14 15:21:23.188 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.04ms
2025-08-14 15:21:23.193 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:21:23.194 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-14 15:21:23.195 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:21:23.196 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 15:21:23.197 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755156083197-88df1358-C
2025-08-14 15:21:23.199 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755156083197-88df1358-C
2025-08-14 15:21:23.200 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:21:23.201 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-14 15:21:23.207 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:21:23.211 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:21:23.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:21:23.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:23.223 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:23.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:21:23.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:21:23.230 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:23.236 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:21:23.251 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:21:23.255 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:21:23.257 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:21:23.258 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:21:23.259 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:21:23.260 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:21:23.262 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:21:23.268 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.269 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.303 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:21:23.305 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:21:23.317 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时12.7ms, 平均每行0.25ms
2025-08-14 15:21:23.318 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.7ms, 策略=small_dataset
2025-08-14 15:21:23.319 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:21:23.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:21:23.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:21:23.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:21:23.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:21:23.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:21:23.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:21:23.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:23.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:23.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:21:23.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:21:23.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:21:23.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:21:23.342 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-14 15:21:23.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 128.6ms
2025-08-14 15:21:23.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:23.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:23.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:21:23.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:23.355 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:21:23.356 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:21:23.362 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:23.380 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-14 15:21:23.384 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:21:23.385 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755156083197-88df1358-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:21:23.386 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156083197-88df1358-C
2025-08-14 15:21:23.387 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156083197-88df1358-C
2025-08-14 15:21:23.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:21:23.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:21:23.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:21:23.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:21:23.392 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:21:23.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:21:23.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:21:23.410 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:21:23.410 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:23.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:21:23.412 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:21:23.476 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:21:23.479 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:21:23.483 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:21:23.484 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:21:23.485 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 15:21:23.486 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:21:23.486 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.490 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:21:23.495 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2023 | 创建年份节点: 2025年，包含 1 个月份
2025-08-14 15:21:23.500 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:21:23.506 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:21:23.507 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 5个展开项
2025-08-14 15:21:23.508 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2163 | force_refresh_salary_data 执行完成
2025-08-14 15:21:23.509 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6236 | 将在1500ms后导航到: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.509 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:6308 | 尝试导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.510 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:6313 | 已成功导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.511 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:21:23.512 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:6283 | 🔧 [P1修复] 安全导航后更新表名: salary_data_2025_08_active_employees
2025-08-14 15:21:23.513 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6401 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_08_active_employees
2025-08-14 15:21:23.513 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6406 | 🔧 [P1修复] 刷新数据时更新表名: salary_data_2025_08_active_employees
2025-08-14 15:21:23.524 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-08-14 15:21:23.527 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-08-14 15:21:23.528 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=1ms
2025-08-14 15:21:23.529 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6428 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-08-14 15:21:23.529 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6502 | [数据流追踪] 执行分页显示模式: salary_data_2025_08_active_employees, 1396条记录
2025-08-14 15:21:23.530 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7530 | 使用分页模式加载 salary_data_2025_08_active_employees，第1页，每页50条
2025-08-14 15:21:23.532 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7682 | 缓存未命中，从数据库加载: salary_data_2025_08_active_employees 第1页
2025-08-14 15:21:23.533 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 开始加载表 salary_data_2025_08_active_employees 第1页数据，每页50条
2025-08-14 15:21:23.533 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6520 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-08-14 15:21:23.535 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:6286 | 同步导航完成: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:21:23.535 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:21:23.548 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-14 15:21:23.549 | INFO     | src.gui.prototype.prototype_main_window:run:182 | 🔧 [P2修复] 使用排序查询: 0 个排序列, 总记录数: 1396
2025-08-14 15:21:23.554 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:21:23.556 | INFO     | src.gui.prototype.prototype_main_window:run:234 | 原始数据: 50行, 28列
2025-08-14 15:21:23.604 | INFO     | src.gui.prototype.prototype_main_window:run:241 | 开始应用字段映射
2025-08-14 15:21:23.611 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6927 | 开始统一字段处理: salary_data_2025_08_active_employees, 原始列数: 28
2025-08-14 15:21:23.614 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-14 15:21:23.615 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:21:23.616 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-14 15:21:23.622 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-14 15:21:23.624 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-14 15:21:23.625 | INFO     | src.modules.format_management.format_config:load_config:403 | 🔧 [格式配置] 配置文件加载成功
2025-08-14 15:21:23.628 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:21:23.628 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-14 15:21:23.633 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-14 15:21:23.642 | INFO     | src.core.architecture_factory:get_unified_format_manager:293 | 🎯 [统一格式管理] 统一格式管理器创建成功
2025-08-14 15:21:23.651 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:23.653 | INFO     | src.modules.format_management.format_renderer:render_dataframe:113 | 🎯 [格式渲染] 已隐藏字段: ['created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:23.665 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-14 15:21:23.666 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=salary_data_2025_08_active_employees, display_fields=24个字段
2025-08-14 15:21:23.667 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '工号' -> 'employee_id'
2025-08-14 15:21:23.668 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '姓名' -> 'employee_name'
2025-08-14 15:21:23.674 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '部门名称' -> 'department'
2025-08-14 15:21:23.683 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '人员类别' -> 'employee_type'
2025-08-14 15:21:23.684 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '人员类别代码' -> 'employee_type_code'
2025-08-14 15:21:23.685 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '2025年岗位工资' -> 'position_salary_2025'
2025-08-14 15:21:23.685 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '2025年薪级工资' -> 'grade_salary_2025'
2025-08-14 15:21:23.686 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '津贴' -> 'allowance'
2025-08-14 15:21:23.687 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '结余津贴' -> 'balance_allowance'
2025-08-14 15:21:23.688 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '应发工资' -> 'total_salary'
2025-08-14 15:21:23.690 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '补发' -> 'supplement'
2025-08-14 15:21:23.691 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '借支' -> 'advance'
2025-08-14 15:21:23.697 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1169 | 🔧 [P2修复] 直接映射成功: '月份' -> 'month'
2025-08-14 15:21:23.697 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 13/24 个字段
2025-08-14 15:21:23.700 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 11个
2025-08-14 15:21:23.704 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:21:23.704 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.706 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.712 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6935 | 🔧 [调试] 格式化时删除的列: {'sequence_number', 'updated_at', 'created_at', 'id'}
2025-08-14 15:21:23.717 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:21:23.719 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6988 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-08-14 15:21:23.719 | INFO     | src.gui.prototype.prototype_main_window:run:251 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-08-14 15:21:23.725 | INFO     | src.gui.prototype.prototype_main_window:run:265 | 字段映射成功: 24列
2025-08-14 15:21:23.727 | INFO     | src.gui.prototype.prototype_main_window:run:291 | 最终数据: 50行, 24列, 总记录数: 1396
2025-08-14 15:21:23.728 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7713 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-08-14 15:21:23.730 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7747 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_08_active_employees 第1页
2025-08-14 15:21:23.741 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:532 | 正在从表 salary_data_2025_08_active_employees 分页获取数据: 第2页, 每页50条
2025-08-14 15:21:23.743 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7776 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-08-14 15:21:23.747 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:21:23.752 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:565 | 成功从表 salary_data_2025_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-08-14 15:21:23.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:21:23.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:23.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:23.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:21:23.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:21:23.785 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:21:23.786 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:21:23.797 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:21:23.799 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:21:23.800 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:21:23.810 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:21:23.811 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:21:23.812 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:21:23.816 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:21:23.817 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.818 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:21:23.857 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:21:23.858 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:21:23.874 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时15.6ms, 平均每行0.31ms
2025-08-14 15:21:23.875 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.6ms, 策略=small_dataset
2025-08-14 15:21:23.876 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:21:23.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:21:23.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:21:23.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:21:23.899 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:21:23.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:21:23.901 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:21:23.902 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:21:23.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:21:23.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:21:23.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:21:23.905 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:21:23.906 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:21:23.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 158.2ms
2025-08-14 15:21:23.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:21:23.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:21:23.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:21:23.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:21:23.937 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-14 15:21:23.938 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7795 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-08-14 15:21:23.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:21:23.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:21:23.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:21:23.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:21:23.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:21:23.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:21:23.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:21:23.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:21:23.969 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:21:23.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:21:23.973 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7797 | 🔍 [调试-分页] set_pagination_state调用完成
2025-08-14 15:21:23.978 | INFO     | src.gui.widgets.pagination_widget:set_total_records:442 | 📊[pagination-write] set_total_records | old_total=1396 -> new_total=1396 | pages_old=28 -> pages_new=28
2025-08-14 15:21:23.985 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7819 | 🔧 [P0-新1修复] 分页状态验证: 当前第1页，共28页，总记录1396条
2025-08-14 15:21:23.990 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7870 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-08-14 15:21:24.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-14 15:26:29.524 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> large
2025-08-14 15:26:29.524 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: large
2025-08-14 15:26:29.951 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: xl (宽度: 1920px)
2025-08-14 15:26:29.952 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:26:30.362 | INFO     | src.gui.table_header_manager:start_shadow_monitoring:892 | 🔧 [P1-1] 表头重影实时监控已启动，间隔: 2000ms
2025-08-14 15:26:39.612 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:26:41.668 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:30:05.165 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:30:09.006 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-14 15:30:09.007 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6031 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-14 15:30:09.010 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-14 15:30:09.012 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-14 15:30:09.012 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-14 15:30:09.026 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:30:09.032 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:30:09.068 | INFO     | src.gui.main_dialogs:_get_template_fields:1874 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-14 15:30:09.071 | INFO     | src.gui.main_dialogs:_init_field_mapping:1861 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-14 15:30:09.113 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-14 15:30:09.114 | INFO     | src.gui.main_dialogs:_apply_default_settings:2212 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-14 15:30:09.117 | INFO     | src.gui.main_dialogs:_setup_tooltips:2467 | 工具提示设置完成
2025-08-14 15:30:09.118 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2506 | 快捷键设置完成
2025-08-14 15:30:09.118 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-14 15:30:09.119 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:84 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-14 15:30:09.121 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6042 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-14 15:30:13.021 | INFO     | src.gui.main_dialogs:_on_target_changed:2151 | 目标位置已更新: 异动人员表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:30:16.421 | INFO     | src.gui.main_dialogs:_on_target_changed:2151 | 目标位置已更新: 异动人员表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:30:25.566 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:30:28.167 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:30:28.167 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-14 15:30:28.171 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2247 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-14 15:31:07.686 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:31:07.897 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-14 15:31:07.898 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:214 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-14 15:31:08.094 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:225 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-14 15:31:08.096 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-14 15:31:08.098 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:31:08.193 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-14 15:31:08.196 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:31:08.198 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-14 15:31:08.199 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-14 15:31:08.199 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:31:08.202 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-14 15:31:08.204 | INFO     | src.modules.data_import.excel_importer:import_data:301 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-14 15:31:08.210 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 离休人员工资表 使用智能默认配置
2025-08-14 15:31:08.211 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-14 15:31:08.212 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-14 15:31:08.212 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-14 15:31:08.220 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-14 15:31:08.222 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_10_retired_employees 自动生成字段类型配置
2025-08-14 15:31:08.223 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_10_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-14 15:31:08.224 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_10_retired_employees
2025-08-14 15:31:08.225 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_10_retired_employees']
2025-08-14 15:31:08.226 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_10_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-14 15:31:08.227 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-14 15:31:08.236 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-14 15:31:08.237 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-14 15:31:08.239 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:31:08.239 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:31:08.241 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-14 15:31:08.243 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-14 15:31:08.244 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_10_retired_employees
2025-08-14 15:31:08.245 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_10_retired_employees
2025-08-14 15:31:08.246 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_10_retired_employees 生成标准化字段映射: 21 个字段
2025-08-14 15:31:08.256 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-14 15:31:08.257 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-08-14 15:31:08.266 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_10_retired_employees
2025-08-14 15:31:08.272 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_10_retired_employees (模板: retired_employees)
2025-08-14 15:31:08.306 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-14 15:31:08.307 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:31:08.364 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-14 15:31:08.382 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。
2025-08-14 15:31:08.388 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:31:08.494 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:31:08.496 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-14 15:31:08.499 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:31:08.500 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-14 15:31:08.501 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 退休人员工资表 使用智能默认配置
2025-08-14 15:31:08.502 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-14 15:31:08.508 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-14 15:31:08.509 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-14 15:31:08.516 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-14 15:31:08.520 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_10_pension_employees 自动生成字段类型配置
2025-08-14 15:31:08.521 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_10_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-14 15:31:08.522 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_10_pension_employees
2025-08-14 15:31:08.524 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_10_pension_employees']
2025-08-14 15:31:08.525 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_10_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-14 15:31:08.525 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-14 15:31:08.533 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-14 15:31:08.535 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-14 15:31:08.536 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:31:08.536 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:31:08.539 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-14 15:31:08.539 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_10_pension_employees
2025-08-14 15:31:08.540 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_10_pension_employees
2025-08-14 15:31:08.541 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_10_pension_employees 生成标准化字段映射: 32 个字段
2025-08-14 15:31:08.545 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-14 15:31:08.547 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-08-14 15:31:08.552 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_10_pension_employees
2025-08-14 15:31:08.563 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_10_pension_employees (模板: pension_employees)
2025-08-14 15:31:08.574 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-08-14 15:31:08.575 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:31:08.577 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-14 15:31:08.588 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。
2025-08-14 15:31:08.588 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:31:08.716 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:31:08.718 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-14 15:31:08.722 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:31:08.725 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-14 15:31:08.730 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-14 15:31:08.731 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-14 15:31:08.732 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-14 15:31:08.733 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-14 15:31:08.744 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-14 15:31:08.746 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_10_active_employees 自动生成字段类型配置
2025-08-14 15:31:08.747 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_10_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-14 15:31:08.747 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_10_active_employees
2025-08-14 15:31:08.751 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", "表 salary_data_2025_10_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_10_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_10_active_employees']
2025-08-14 15:31:08.752 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_10_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:31:08.758 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-14 15:31:08.766 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-14 15:31:08.767 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-14 15:31:08.773 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:31:08.774 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:31:08.778 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-14 15:31:08.779 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_10_active_employees
2025-08-14 15:31:08.779 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_10_active_employees
2025-08-14 15:31:08.784 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_10_active_employees 生成标准化字段映射: 28 个字段
2025-08-14 15:31:08.796 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-14 15:31:08.797 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-08-14 15:31:08.802 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_10_active_employees
2025-08-14 15:31:08.810 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_10_active_employees (模板: active_employees)
2025-08-14 15:31:08.824 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-14 15:31:08.826 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:31:08.828 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-14 15:31:08.868 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。
2025-08-14 15:31:08.871 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-14 15:31:08.993 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-14 15:31:08.994 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-14 15:31:08.998 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-14 15:31:09.000 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-14 15:31:09.005 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:459 | 工作表 A岗职工 使用智能默认配置
2025-08-14 15:31:09.006 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:747 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-14 15:31:09.007 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-14 15:31:09.007 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:476 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-14 15:31:09.017 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-14 15:31:09.019 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_10_a_grade_employees 自动生成字段类型配置
2025-08-14 15:31:09.019 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_10_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-14 15:31:09.020 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_10_a_grade_employees
2025-08-14 15:31:09.022 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", "表 salary_data_2025_10_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_10_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_10_a_grade_employees']
2025-08-14 15:31:09.023 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2055 | 🔧 [P1-1修复] 表 salary_data_2025_10_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-14 15:31:09.024 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2065 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-14 15:31:09.033 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-14 15:31:09.034 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-14 15:31:09.035 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-14 15:31:09.035 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-14 15:31:09.038 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-14 15:31:09.039 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_10_a_grade_employees
2025-08-14 15:31:09.040 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_10_a_grade_employees
2025-08-14 15:31:09.041 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:523 | 为表 salary_data_2025_10_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-14 15:31:09.046 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:544 | Sheet A岗职工 数据处理完成: 62 行
2025-08-14 15:31:09.048 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:374 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-08-14 15:31:09.053 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:837 | 成功创建表: salary_data_2025_10_a_grade_employees
2025-08-14 15:31:09.059 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:453 | 专用工资数据表创建成功: salary_data_2025_10_a_grade_employees (模板: a_grade_employees)
2025-08-14 15:31:09.068 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1628 | [FIX] [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-08-14 15:31:09.073 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1636 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-14 15:31:09.075 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1645 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-14 15:31:09.089 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1782 | 成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。
2025-08-14 15:31:09.090 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:244 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-14 15:31:09.091 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1481 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-10', 'data_description': '2025年10月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '异动人员表 > 2025年 > 10月 > 全部在职人员'}
2025-08-14 15:31:09.095 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6061 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-10', 'data_description': '2025年10月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '异动人员表 > 2025年 > 10月 > 全部在职人员'}
2025-08-14 15:31:09.102 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6072 | 导入模式: multi_sheet, 目标路径: '异动人员表 > 2025年 > 10月 > 全部在职人员'
2025-08-14 15:31:09.103 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6090 | 接收到导入数据, 来源: 未知来源, 目标路径: 异动人员表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:31:09.107 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1740 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-14 15:31:09.117 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-14 15:31:09.120 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 异动人员表
2025-08-14 15:31:09.121 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:31:09.122 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:09.122 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 1 个字段处理缓存条目
2025-08-14 15:31:09.125 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7978 | 🔧 [表名生成] 路径第一级不是'工资表': 异动人员表
2025-08-14 15:31:09.126 | INFO     | src.gui.prototype.prototype_main_window:set_data:798 | 空数据输入发生 5 次（2s 窗口），将显示空表提示
2025-08-14 15:31:09.127 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:31:09.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:31:09.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:31:09.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:31:09.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:31:09.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:31:09.158 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:31:09.158 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:31:09.159 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:31:09.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:31:09.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:31:09.165 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-14 15:31:09.168 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-14 15:31:09.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 15.5ms
2025-08-14 15:31:09.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:09.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:09.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:09.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:09.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:09.180 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:31:09.181 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:09.181 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8153 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-14 15:31:09.182 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-14 15:31:09.184 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:31:09.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:31:09.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:31:09.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:31:09.187 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:31:09.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:31:09.194 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:31:09.194 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:31:09.195 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:31:09.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:31:09.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 11.7ms
2025-08-14 15:31:09.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:09.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:09.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:09.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:09.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:09.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:09.207 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:31:09.209 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 22
2025-08-14 15:31:09.224 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 8 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:31:09.233 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:31:09.234 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:31:09.234 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:31:09.235 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:31:09.238 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表']
2025-08-14 15:31:09.243 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-14 15:31:09.244 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1788 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-14 15:31:09.244 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:31:09.249 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1740 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-14 15:31:09.251 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:31:09.255 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:31:09.257 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:31:09.257 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:09.258 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:09.258 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:31:09.259 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:09.260 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-14 15:31:09.265 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 6 个表格到表头管理器
2025-08-14 15:31:09.270 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 6 个表格
2025-08-14 15:31:09.272 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.56ms
2025-08-14 15:31:09.272 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:31:09.273 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-14 15:31:09.273 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:31:09.274 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:31:09.274 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:31:09.278 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-14 15:31:09.279 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=5.1ms
2025-08-14 15:31:09.285 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-14 15:31:09.285 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:09.286 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:09.298 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:09.299 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:09.303 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:09.307 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:09.307 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:09.308 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 25/25 个字段
2025-08-14 15:31:09.309 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 3个
2025-08-14 15:31:09.311 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-14 15:31:09.311 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:31:09.312 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:31:09.313 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-14 15:31:09.314 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-14 15:31:09.314 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755156669314-4fc306bd
2025-08-14 15:31:09.315 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755156669314-4fc306bd
2025-08-14 15:31:09.315 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:31:09.325 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-14 15:31:09.330 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:09.331 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:31:09.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:31:09.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:31:09.342 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:31:09.346 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:09.346 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:09.362 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:09.365 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:09.367 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:09.368 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:09.368 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:09.369 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:31:09.371 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:31:09.371 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.371 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.399 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:31:09.400 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:31:09.412 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时11.8ms, 平均每行0.24ms
2025-08-14 15:31:09.413 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=11.8ms, 策略=small_dataset
2025-08-14 15:31:09.413 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:09.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:31:09.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:31:09.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:31:09.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:31:09.415 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:31:09.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:31:09.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:31:09.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:31:09.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:31:09.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:31:09.419 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-14 15:31:09.423 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-14 15:31:09.429 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 79.8ms
2025-08-14 15:31:09.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:09.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:09.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:31:09.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:09.432 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:09.433 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:31:09.433 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.433 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-14 15:31:09.434 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:31:09.435 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755156669314-4fc306bd | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:31:09.435 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156669314-4fc306bd
2025-08-14 15:31:09.435 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156669314-4fc306bd
2025-08-14 15:31:09.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:09.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:31:09.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:31:09.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:31:09.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:31:09.438 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:31:09.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:31:09.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:31:09.448 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:31:09.449 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:09.506 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_3_1891957134192 已自动清理（弱引用回调）
2025-08-14 15:31:09.506 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_2_1891957134768 已自动清理（弱引用回调）
2025-08-14 15:31:09.526 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:09.527 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:09.527 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:09.528 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:09.528 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 15:31:09.529 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:09.539 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 8 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:31:09.545 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 8 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:31:09.554 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1712 | 动态加载了 2 个月份的工资数据导航
2025-08-14 15:31:09.560 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:31:09.561 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:31:09.568 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:31:09.569 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:31:09.569 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:31:09.570 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:09.571 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:09.571 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '10月', '全部在职人员'] -> salary_data_2025_10_active_employees
2025-08-14 15:31:09.572 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:09.572 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_10_active_employees 的缓存
2025-08-14 15:31:09.575 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:31:09.575 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_10_active_employees（通过事件系统）
2025-08-14 15:31:09.576 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:31:09.582 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_10_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:31:09.585 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_10_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-14 15:31:09.586 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=10.7ms
2025-08-14 15:31:09.587 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:09.587 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:09.596 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:09.597 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:09.599 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:09.602 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:09.605 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:09.606 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 25/25 个字段
2025-08-14 15:31:09.606 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 3个
2025-08-14 15:31:09.608 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-14 15:31:09.608 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:31:09.609 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-14 15:31:09.610 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_10_active_employees | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755156669610-1f79f909
2025-08-14 15:31:09.611 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_10_active_employees | request_id=SV-1755156669610-1f79f909
2025-08-14 15:31:09.612 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:31:09.612 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_10_active_employees, 50行
2025-08-14 15:31:09.616 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:09.621 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:31:09.623 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:09.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_10_active_employees
2025-08-14 15:31:09.631 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:31:09.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:31:09.636 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:09.636 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:09.650 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:09.651 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:09.652 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:09.653 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:09.654 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:09.654 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:31:09.655 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:31:09.660 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.660 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.680 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:31:09.681 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:31:09.690 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时9.1ms, 平均每行0.18ms
2025-08-14 15:31:09.691 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.1ms, 策略=small_dataset
2025-08-14 15:31:09.692 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:09.695 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:31:09.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:31:09.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:31:09.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:31:09.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:31:09.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:31:09.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.698 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:31:09.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:31:09.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:31:09.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:31:09.701 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_10_active_employees
2025-08-14 15:31:09.703 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_10_active_employees 重新加载 28 个字段映射
2025-08-14 15:31:09.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 73.3ms
2025-08-14 15:31:09.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:09.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:09.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:09.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:09.714 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:09.714 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:31:09.715 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_10_active_employees, 传递参数: 24个表头
2025-08-14 15:31:09.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:31:09.716 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755156669610-1f79f909 | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:31:09.716 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156669610-1f79f909
2025-08-14 15:31:09.717 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156669610-1f79f909
2025-08-14 15:31:09.717 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:09.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:31:09.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:31:09.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:31:09.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:31:09.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:09.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_10_active_employees
2025-08-14 15:31:09.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:31:09.731 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:31:09.732 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:09.782 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:09.783 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:09.785 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:09.786 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:09.786 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 15:31:09.787 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:09.787 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:31:09.788 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:31:09.788 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:31:09.789 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:31:09.790 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-14 15:31:09.790 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_10_active_employees -> None
2025-08-14 15:31:09.791 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:09.792 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:09.792 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-14 15:31:09.804 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:09.805 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-14 15:31:09.805 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:31:09.808 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-14 15:31:09.809 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 15:31:09.809 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755156669809-eba42d01-C
2025-08-14 15:31:09.810 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755156669809-eba42d01-C
2025-08-14 15:31:09.811 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:31:09.811 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-14 15:31:09.817 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:09.823 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:31:09.834 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:09.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-14 15:31:09.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:31:09.855 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:31:09.858 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:09.859 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:09.878 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:09.881 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:09.883 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:09.884 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:09.884 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:09.885 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:31:09.887 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:31:09.887 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.888 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:09.909 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:31:09.910 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:31:09.920 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时10.8ms, 平均每行0.22ms
2025-08-14 15:31:09.922 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=10.8ms, 策略=small_dataset
2025-08-14 15:31:09.922 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:09.923 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:31:09.923 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:31:09.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:31:09.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:31:09.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:31:09.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:31:09.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:09.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:09.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:31:09.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:31:09.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:31:09.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:31:09.929 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-14 15:31:09.931 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-14 15:31:09.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 88.9ms
2025-08-14 15:31:09.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:09.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:09.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:31:09.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:09.942 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:09.942 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:31:09.942 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.943 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-14 15:31:09.943 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:31:09.944 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755156669809-eba42d01-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:31:09.944 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156669809-eba42d01-C
2025-08-14 15:31:09.945 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156669809-eba42d01-C
2025-08-14 15:31:09.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:09.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:31:09.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:31:09.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:31:09.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:31:09.955 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-14 15:31:09.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-14 15:31:09.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:31:09.957 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:09.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:31:09.958 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:09.999 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:10.000 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:10.002 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:10.002 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:10.003 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 15:31:10.003 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:10.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 5个展开项
2025-08-14 15:31:10.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1788 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-14 15:31:10.005 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:31:10.009 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1295 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-14 15:31:10.010 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1529 | 开始获取最新工资数据路径...
2025-08-14 15:31:10.016 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 8 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:31:10.018 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1577 | 找到最新工资数据路径: 工资表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:31:10.018 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1453 | 🔧 [P1-2修复] 成功获取到最新路径
2025-08-14 15:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 10月 > 全部在职人员
2025-08-14 15:31:10.020 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-14 15:31:10.021 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:10.021 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:10.022 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7227 | 检测到自动选择的最新数据，将显示特殊提示
2025-08-14 15:31:10.023 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '10月', '全部在职人员'] -> salary_data_2025_10_active_employees
2025-08-14 15:31:10.024 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:10.024 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_10_active_employees 的缓存
2025-08-14 15:31:10.027 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:31:10.032 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_10_active_employees（通过事件系统）
2025-08-14 15:31:10.032 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_10_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755156670032-f1837c4a-C
2025-08-14 15:31:10.033 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_10_active_employees | request_id=SV-1755156670032-f1837c4a-C
2025-08-14 15:31:10.033 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-14 15:31:10.033 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_10_active_employees, 50行
2025-08-14 15:31:10.038 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:10.039 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:31:10.041 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:10.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_10_active_employees
2025-08-14 15:31:10.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:10.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:10.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-14 15:31:10.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:31:10.060 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['row_number', 'created_at', 'sequence_number', 'updated_at', 'id']
2025-08-14 15:31:10.060 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:10.072 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:10.073 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:10.074 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:10.076 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-14 15:31:10.076 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-14 15:31:10.077 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-14 15:31:10.078 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-14 15:31:10.081 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:10.082 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-14 15:31:10.102 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:31:10.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-14 15:31:10.114 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时10.7ms, 平均每行0.21ms
2025-08-14 15:31:10.116 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=10.7ms, 策略=small_dataset
2025-08-14 15:31:10.117 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:10.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-14 15:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-14 15:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-14 15:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-14 15:31:10.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-14 15:31:10.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:31:10.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-14 15:31:10.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-14 15:31:10.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-14 15:31:10.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-14 15:31:10.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-14 15:31:10.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-14 15:31:10.123 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_10_active_employees
2025-08-14 15:31:10.125 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_10_active_employees 重新加载 28 个字段映射
2025-08-14 15:31:10.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 73.5ms
2025-08-14 15:31:10.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:10.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:10.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:10.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:10.135 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:10.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-14 15:31:10.136 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:10.137 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_10_active_employees, 传递参数: 24个表头
2025-08-14 15:31:10.137 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-14 15:31:10.138 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755156670032-f1837c4a-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-14 15:31:10.138 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755156670032-f1837c4a-C
2025-08-14 15:31:10.139 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156670032-f1837c4a-C
2025-08-14 15:31:10.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:10.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-14 15:31:10.140 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:31:10.140 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-14 15:31:10.140 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:31:10.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:10.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_10_active_employees
2025-08-14 15:31:10.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:31:10.152 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-14 15:31:10.153 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:31:10.153 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:10.203 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:10.204 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:10.206 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:10.206 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:10.207 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 15:31:10.207 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:10.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:10.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_10_active_employees
2025-08-14 15:31:10.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_active_employees 的列宽配置
2025-08-14 15:31:10.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_10_active_employees
2025-08-14 15:31:10.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:10.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:10.250 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:10.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:10.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:10.542 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_active_employees
2025-08-14 15:31:11.005 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6223 | 检查是否需要更新导航面板: ['异动人员表', '2025年', '10月', '全部在职人员']
2025-08-14 15:31:11.005 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6260 | 非工资数据导入，使用标准刷新
2025-08-14 15:31:11.008 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1740 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-14 15:31:11.010 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表', '工资表 > 2025年 > 10月 > 全部在职人员']
2025-08-14 15:31:11.010 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 异动人员表
2025-08-14 15:31:11.011 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_10_active_employees -> None
2025-08-14 15:31:11.012 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:11.012 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:11.013 | WARNING  | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7978 | 🔧 [表名生成] 路径第一级不是'工资表': 异动人员表
2025-08-14 15:31:11.014 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:31:11.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:31:11.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:31:11.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:31:11.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:31:11.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:31:11.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:31:11.025 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:31:11.026 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:31:11.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:31:11.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-14 15:31:11.027 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-14 15:31:11.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 11.9ms
2025-08-14 15:31:11.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:11.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:11.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:11.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:11.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:11.041 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:31:11.042 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:11.042 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8153 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-14 15:31:11.042 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8236 | 🔧 [数据流追踪] 使用表 salary_data_2025_10_active_employees 的专用表头: 22个字段
2025-08-14 15:31:11.043 | WARNING  | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [P0-修复] 未找到可用表，返回占位符
2025-08-14 15:31:11.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-14 15:31:11.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-14 15:31:11.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-08-14 15:31:11.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-14 15:31:11.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-14 15:31:11.052 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-14 15:31:11.052 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-14 15:31:11.053 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-14 15:31:11.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-14 15:31:11.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 10.8ms
2025-08-14 15:31:11.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:11.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:11.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:11.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:11.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-14 15:31:11.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:11.057 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-14 15:31:11.058 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8258 | 已显示标准空表格，表头数量: 22
2025-08-14 15:31:11.064 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 8 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 15:31:11.075 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-14 15:31:11.075 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1435 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-14 15:31:11.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-14 15:31:11.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1835 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 15:31:11.080 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表', '工资表 > 2025年 > 10月 > 全部在职人员']
2025-08-14 15:31:11.082 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1866 | 🔧 [P2-2] 导航状态恢复完成: 1个展开项
2025-08-14 15:31:11.082 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1788 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-14 15:31:11.083 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:6264 | 导航面板已刷新
2025-08-14 15:31:11.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:11.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-14 15:31:11.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-14 15:31:11.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-14 15:31:11.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-14 15:31:11.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-14 15:31:11.140 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:31:11.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-14 15:31:26.007 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:31:37.969 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:31:37.970 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 10月 > 离休人员
2025-08-14 15:31:37.972 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_10_active_employees -> None
2025-08-14 15:31:37.973 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:37.974 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:37.975 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '10月', '离休人员'] -> salary_data_2025_10_retired_employees
2025-08-14 15:31:37.975 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:37.976 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_10_retired_employees 的缓存
2025-08-14 15:31:37.980 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 4 个表格到表头管理器
2025-08-14 15:31:37.985 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 6 个表格
2025-08-14 15:31:37.986 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.03ms
2025-08-14 15:31:37.987 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:31:37.987 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_10_retired_employees（通过事件系统）
2025-08-14 15:31:37.988 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:31:37.988 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:31:37.990 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_10_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:31:37.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_10_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-14 15:31:37.993 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=4.7ms
2025-08-14 15:31:37.994 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-14 15:31:38.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'updated_at', 'created_at', 'id']
2025-08-14 15:31:38.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:38.005 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 15:31:38.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:38.007 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:38.012 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 15:31:38.012 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 15:31:38.015 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 15:31:38.016 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 15:31:38.016 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 15:31:38.017 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-14 15:31:38.018 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-08-14 15:31:38.019 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 15:31:38.019 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 15:31:38.022 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_10_retired_employees, 变更类型: None
2025-08-14 15:31:38.028 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-08-14 15:31:38.030 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_10_retired_employees | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755156698028-8f7ae923
2025-08-14 15:31:38.030 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_10_retired_employees | request_id=SV-1755156698028-8f7ae923
2025-08-14 15:31:38.031 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 15:31:38.031 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_10_retired_employees, 2行
2025-08-14 15:31:38.035 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:38.036 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 15:31:38.044 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_10_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:38.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_10_retired_employees
2025-08-14 15:31:38.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 15:31:38.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 15:31:38.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 10
2025-08-14 15:31:38.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 15:31:38.057 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'updated_at', 'created_at', 'id']
2025-08-14 15:31:38.058 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:38.062 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 15:31:38.064 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:38.067 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:38.069 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 15:31:38.071 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 15:31:38.071 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 15:31:38.072 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 15:31:38.073 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 15:31:38.075 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 15:31:38.075 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 15:31:38.076 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 15:31:38.079 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 15:31:38.084 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 15:31:38.085 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.1ms, 平均每行0.53ms
2025-08-14 15:31:38.085 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=1.1ms, 策略=small_dataset
2025-08-14 15:31:38.086 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:38.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 15:31:38.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 15:31:38.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 15:31:38.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 15:31:38.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 15:31:38.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 15:31:38.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 15:31:38.089 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_10_retired_employees
2025-08-14 15:31:38.092 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_10_retired_employees 重新加载 21 个字段映射
2025-08-14 15:31:38.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 41.8ms
2025-08-14 15:31:38.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:38.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:38.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_retired_employees 的列宽配置
2025-08-14 15:31:38.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:38.103 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:38.103 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 15:31:38.104 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 15:31:38.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_10_retired_employees, 传递参数: 17个表头
2025-08-14 15:31:38.105 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 15:31:38.105 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755156698028-8f7ae923 | total: 0->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 15:31:38.106 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755156698028-8f7ae923
2025-08-14 15:31:38.107 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755156698028-8f7ae923
2025-08-14 15:31:38.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:38.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 15:31:38.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 15:31:38.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 15:31:38.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 15:31:38.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_10_retired_employees 的列宽配置
2025-08-14 15:31:38.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_10_retired_employees
2025-08-14 15:31:38.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 15:31:38.119 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 15:31:38.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 15:31:38.119 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:38.177 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_3_1892276576640 已自动清理（弱引用回调）
2025-08-14 15:31:38.180 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_5_1892276576640 已自动清理（弱引用回调）
2025-08-14 15:31:38.187 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_2_1892276577216 已自动清理（弱引用回调）
2025-08-14 15:31:38.190 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_4_1892276577216 已自动清理（弱引用回调）
2025-08-14 15:31:38.240 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:38.241 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:38.255 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:38.271 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:38.273 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 15:31:38.274 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:38.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_10_retired_employees
2025-08-14 15:31:40.711 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 08月', '工资表 > 2025年 > 05月 > 离休人员']
2025-08-14 15:31:40.712 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7178 | 导航变化: 工资表 > 2025年 > 08月 > A岗职工
2025-08-14 15:31:40.714 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10231 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_10_retired_employees -> None
2025-08-14 15:31:40.715 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10247 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 15:31:40.716 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7053 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-14 15:31:40.716 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8052 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', 'A岗职工'] -> salary_data_2025_08_a_grade_employees
2025-08-14 15:31:40.717 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 15:31:40.717 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_a_grade_employees 的缓存
2025-08-14 15:31:40.719 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10917 | 已注册 2 个表格到表头管理器
2025-08-14 15:31:40.719 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-14 15:31:40.720 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.87ms
2025-08-14 15:31:40.721 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 15:31:40.721 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7394 | 🆕 使用新架构加载数据: salary_data_2025_08_a_grade_employees（通过事件系统）
2025-08-14 15:31:40.721 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 15:31:40.722 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 15:31:40.725 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_a_grade_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 15:31:40.728 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_a_grade_employees 获取第1页数据（含排序）: 50 行，总计62行
2025-08-14 15:31:40.728 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=26, 行数=50, 耗时=6.5ms
2025-08-14 15:31:40.728 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-14 15:31:40.728 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'updated_at', 'created_at', 'id']
2025-08-14 15:31:40.728 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:40.728 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/22 个字段
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始26个 -> 最终26个字段
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 26
2025-08-14 15:31:40.744 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 26
2025-08-14 15:31:40.744 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_a_grade_employees, 变更类型: None
2025-08-14 15:31:40.744 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-14 15:31:40.760 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_a_grade_employees | rows=50 | page=1 | size=50 | total=62 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755156700760-77b478f2
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_a_grade_employees | request_id=SV-1755156700760-77b478f2
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 26列
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_a_grade_employees, 50行
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-14 15:31:40.760 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7135 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 15:31:40.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_a_grade_employees
2025-08-14 15:31:40.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024, 薪资=N/A
2025-08-14 15:31:40.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002, 薪资=N/A
2025-08-14 15:31:40.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 50
2025-08-14 15:31:40.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-14 15:31:40.776 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'updated_at', 'created_at', 'id']
2025-08-14 15:31:40.776 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 15:31:40.791 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 15:31:40.791 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 15:31:40.791 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-14 15:31:40.791 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-14 15:31:40.807 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-14 15:31:40.807 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 22/22 个字段
2025-08-14 15:31:40.807 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始22个 -> 最终22个字段
2025-08-14 15:31:40.807 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-14 15:31:40.807 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-14 15:31:40.822 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-14 15:31:40.852 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-08-14 15:31:40.863 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时11.2ms, 平均每行0.22ms
2025-08-14 15:31:40.863 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=11.2ms, 策略=small_dataset
2025-08-14 15:31:40.864 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 15:31:40.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 34660024
2025-08-14 15:31:40.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20222002
2025-08-14 15:31:40.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 14660141
2025-08-14 15:31:40.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 34660002
2025-08-14 15:31:40.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 34660010
2025-08-14 15:31:40.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-14 15:31:40.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-08-14 15:31:40.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-08-14 15:31:40.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-08-14 15:31:40.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-08-14 15:31:40.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-08-14 15:31:40.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-08-14 15:31:40.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 22 列
2025-08-14 15:31:40.871 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_a_grade_employees
2025-08-14 15:31:40.881 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_a_grade_employees 重新加载 26 个字段映射
2025-08-14 15:31:40.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 95.2ms
2025-08-14 15:31:40.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 15:31:40.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 15:31:40.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_a_grade_employees 的列宽配置
2025-08-14 15:31:40.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 15:31:40.885 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-14 15:31:40.886 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-08-14 15:31:40.886 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-14 15:31:40.886 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_a_grade_employees, 传递参数: 22个表头
2025-08-14 15:31:40.887 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-08-14 15:31:40.887 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755156700760-77b478f2 | total: 0->62, page: 1->1, size: 50->50, pages: 1->2
2025-08-14 15:31:40.887 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=2 / total=62 | rid=SV-1755156700760-77b478f2
2025-08-14 15:31:40.888 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755156700760-77b478f2
2025-08-14 15:31:40.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 15:31:40.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 62, 'start_record': 1, 'end_record': 50}
2025-08-14 15:31:40.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-14 15:31:40.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-14 15:31:40.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-14 15:31:40.899 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_a_grade_employees 的列宽配置
2025-08-14 15:31:40.899 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_a_grade_employees
2025-08-14 15:31:40.900 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-14 15:31:40.900 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-14 15:31:40.901 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-14 15:31:40.901 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 15:31:40.944 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 15:31:40.945 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10725 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 15:31:40.947 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10688 | 🆕 [新架构] 导航迁移完成
2025-08-14 15:31:40.947 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 15:31:40.948 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 15:31:40.948 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7399 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 15:31:40.948 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 08月 > A岗职工
2025-08-14 15:31:41.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_a_grade_employees
2025-08-14 15:31:48.919 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:31:51.238 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 15:31:59.458 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-14 15:31:59.464 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_1894072817936 已自动清理（弱引用回调）
